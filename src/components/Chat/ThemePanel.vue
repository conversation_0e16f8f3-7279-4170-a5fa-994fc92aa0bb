<template>
  <div class="theme-panel-popup" @click.stop>
    <!-- 主题色彩 -->
    <div class="palette-section">
      <div class="palette-title">主题色彩</div>
      <div class="palette-grid">
        <div
          v-for="p in palettePresets"
          :key="p.id"
          class="theme-item"
          :title="p.name"
          @click="handleThemeSelect(selectedTone + '-' + p.id)"
        >
          <!-- 使用SVG图标替换swatches -->
          <svg
            class="theme-icon"
            :style="{ color: p.baseColor }"
            t="1755659729601"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
          >
            <path
              d="M512 85.333333c235.605333 0 426.666667 169.728 426.666667 379.264a237.141333 237.141333 0 0 1-237.056 237.013334h-83.882667c-39.338667 0-71.125333 31.786667-71.125333 71.125333 0 18.005333 7.125333 34.602667 18.005333 46.933333 11.392 12.8 18.517333 29.397333 18.517333 47.872C583.125333 906.922667 550.4 938.666667 512 938.666667 276.394667 938.666667 85.333333 747.605333 85.333333 512S276.394667 85.333333 512 85.333333zM320 512a64 64 0 1 0 0-128 64 64 0 0 0 0 128z m384 0a64 64 0 1 0 0-128 64 64 0 0 0 0 128zM512 384a64 64 0 1 0 0-128 64 64 0 0 0 0 128z"
              fill="currentColor"
            />
          </svg>
          <span class="theme-label">{{ p.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useThemeStore } from '@/stores/theme';
import { PALETTE_PRESETS, type ToneMode } from '@/config/theme-presets';

// Props
interface IProps {
  visible: boolean;
}

defineProps<IProps>();

// Emits
const emit = defineEmits<{
  close: [];
  themeSelect: [themeId: string];
}>();

// Store
const themeStore = useThemeStore();

// Data
const selectedTone = ref<ToneMode>('soft');
const palettePresets = PALETTE_PRESETS;

// Methods
const handleThemeSelect = (themeId: string) => {
  void themeStore.switchTheme(themeId);
  emit('themeSelect', themeId);
  emit('close');
  console.log(`🎨 [ThemePanel] 选择主题: ${themeId}`);
};
</script>

<style lang="scss" scoped>
// 主题面板弹窗（独立弹窗样式）
.theme-panel-popup {
  position: fixed; // 改为fixed定位，确保不被遮挡
  right: 20px; // 距离右边缘20px
  top: 120px; // 距离顶部120px，避开header
  min-width: 320px;
  max-height: 60vh;
  overflow: auto;
  padding: 20px;
  background: #ffffff; // 白色背景
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 3000; // 提高z-index确保在助手层之上
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;

  .palette-section {
    .palette-title {
      color: #374151; // 灰黑色
      font-size: 22px; // 原来16px + 6px
      font-weight: 600;
      margin-bottom: 16px;
      text-align: center;
    }

    .palette-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr); // 两种主题为一行
      gap: 12px;
    }
  }
}

.theme-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px 8px;
  border-radius: 12px;
  cursor: pointer;
  transition:
    background 0.2s ease,
    transform 0.05s ease;
  background: #f9fafb;

  &:hover {
    background: #f3f4f6;
    transform: translateY(-2px);
  }

  &.active {
    outline: 2px solid #3b82f6;
    background: #eff6ff;
  }

  .theme-icon {
    width: 24px;
    height: 24px;
    transition: transform 0.2s ease;
  }

  &:hover .theme-icon {
    transform: scale(1.1);
  }

  .theme-label {
    color: #374151; // 灰黑色
    font-size: 20px; // 原来14px + 6px
    font-weight: 500;
    text-align: center;
  }
}
</style>
