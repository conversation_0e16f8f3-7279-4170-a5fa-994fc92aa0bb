<template>
  <div class="theme-palette-popup" @click.stop>
    <!-- 主题色彩 -->
    <div class="palette-section">
      <div class="palette-title">主题色彩</div>
      <div class="palette-grid">
        <div
          v-for="p in palettePresets"
          :key="p.id"
          class="theme-item"
          :title="p.name"
          @click="handleThemeSelect(p.id)"
        >
          <!-- 使用SVG图标替换swatches -->
          <svg
            class="theme-icon"
            :style="{ color: p.baseColor }"
            t="1755659729601"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
          >
            <path
              d="M512 85.333333c235.605333 0 426.666667 169.728 426.666667 379.264a237.141333 237.141333 0 0 1-237.056 237.013334h-83.882667c-39.338667 0-71.125333 31.786667-71.125333 71.125333 0 18.005333 7.125333 34.602667 18.005333 46.933333 11.392 12.8 18.517333 29.397333 18.517333 47.872C583.125333 906.922667 550.4 938.666667 512 938.666667 276.394667 938.666667 85.333333 747.605333 85.333333 512S276.394667 85.333333 512 85.333333zM320 512a64 64 0 1 0 0-128 64 64 0 0 0 0 128z m384 0a64 64 0 1 0 0-128 64 64 0 0 0 0 128zM512 384a64 64 0 1 0 0-128 64 64 0 0 0 0 128z"
              fill="currentColor"
            />
          </svg>
          <span class="theme-label">{{ p.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PALETTE_PRESETS, type ToneMode } from '@/config/theme-presets';

interface IProps {
  selectedTone?: ToneMode;
}

const props = withDefaults(defineProps<IProps>(), {
  selectedTone: 'soft',
});

const emit = defineEmits<{
  themeSelect: [themeId: string];
}>();

const palettePresets = PALETTE_PRESETS;

const handleThemeSelect = (paletteId: string) => {
  const themeId = `${props.selectedTone}-${paletteId}`;
  emit('themeSelect', themeId);
};
</script>

<style lang="scss" scoped>
// 主题面板弹窗（在welcome-container内部的样式）
.theme-palette-popup {
  position: absolute !important; // 使用absolute定位，相对于welcome-container
  right: 20px !important; // 距离右边缘的距离
  top: 80px !important; // 距离顶部的距离，避开header区域
  min-width: 320px;
  max-width: 400px; // 限制最大宽度，避免过宽
  max-height: 60vh;
  overflow: auto;
  padding: 20px;
  background: #ffffff !important; // 白色背景
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.25), // 主阴影
    0 0 0 1px rgba(255, 255, 255, 0.1), // 内边框高光
    0 0 100px rgba(0, 0, 0, 0.1); // 外围光晕，进一步突出层级
  z-index: 1000 !important; // 在welcome-container内部使用较高的z-index
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  isolation: isolate; // 创建独立的层叠上下文
  // 完全移除3D变换，避免与feature-item的3D上下文冲突
  transform: none !important; // 强制移除任何可能的3D变换
  transform-style: flat !important; // 确保不创建3D上下文
  will-change: auto !important; // 移除GPU加速，避免层级问题

  .palette-section {
    .palette-title {
      color: #374151; // 灰黑色
      font-size: 22px; // 原来16px + 6px
      font-weight: 600;
      margin-bottom: 16px;
      text-align: center;
    }

    .palette-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr); // 两种主题为一行
      gap: 12px;
    }
  }
}

.theme-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px 8px;
  border-radius: 12px;
  cursor: pointer;
  transition:
    background 0.2s ease,
    transform 0.05s ease;
  background: #f9fafb;

  &:hover {
    background: #f3f4f6;
    transform: translateY(-2px);
  }

  &.active {
    outline: 2px solid #3b82f6;
    background: #eff6ff;
  }

  .theme-icon {
    width: 24px;
    height: 24px;
    transition: transform 0.2s ease;
  }

  &:hover .theme-icon {
    transform: scale(1.1);
  }

  .theme-label {
    color: #374151; // 灰黑色
    font-size: 20px; // 原来14px + 6px
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
  }
}
</style>
