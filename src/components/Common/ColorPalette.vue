<template>
  <div class="color-palette-overlay" @click="$emit('close')">
    <div class="color-palette-popup" @click.stop>
      <div class="palette-header">
        <h3>选择配色方案</h3>
        <button class="close-btn" @click="$emit('close')">×</button>
      </div>

      <div class="palette-content">
        <!-- 氛围样式选择 -->
        <div class="tone-section">
          <h4>氛围样式</h4>
          <div class="tone-options">
            <button
              v-for="tone in toneOptions"
              :key="tone.id"
              :class="['tone-btn', { active: selectedTone === tone.id }]"
              @click="selectedTone = tone.id"
            >
              {{ tone.name }}
            </button>
          </div>
        </div>

        <!-- 主题色系选择 -->
        <div class="palette-section">
          <h4>主题色系</h4>
          <div class="color-grid">
            <button
              v-for="palette in palettePresets"
              :key="palette.id"
              :class="['color-item', { active: selectedPalette === palette.id }]"
              :style="{ backgroundColor: palette.baseColor }"
              @click="selectedPalette = palette.id"
            >
              <span class="color-name">{{ palette.name }}</span>
            </button>
          </div>
        </div>

        <!-- 预览区域 -->
        <div class="preview-section">
          <h4>预览效果</h4>
          <div class="preview-card" :style="previewStyle">
            <div class="preview-text primary">主要文字</div>
            <div class="preview-text secondary">次要文字</div>
            <button class="preview-button">按钮样式</button>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button class="cancel-btn" @click="$emit('close')">取消</button>
          <button class="apply-btn" @click="applyTheme">应用主题</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useThemeStore } from '@/stores/theme';
import { PALETTE_PRESETS, TONE_OPTIONS, type ToneMode } from '@/config/theme-presets';

defineEmits(['close']);

const themeStore = useThemeStore();

// 当前选择
const selectedTone = ref<ToneMode>('vivid');
const selectedPalette = ref('warm-orange');

// 选项数据
const toneOptions = TONE_OPTIONS;
const palettePresets = PALETTE_PRESETS;

// 预览样式
const previewStyle = computed(() => {
  const palette = palettePresets.find((p) => p.id === selectedPalette.value);
  if (!palette) return {};

  const { baseColor } = palette;

  // 根据氛围样式生成预览背景
  let bgColor = '';
  let textColor = '';

  switch (selectedTone.value) {
    case 'soft':
      bgColor = `rgba(${hexToRgb(baseColor)}, 0.1)`;
      textColor = '#374151';
      break;
    case 'vivid':
      bgColor = `rgba(${hexToRgb(baseColor)}, 0.3)`;
      textColor = '#ffffff';
      break;
    case 'bright':
      bgColor = `rgba(${hexToRgb(baseColor)}, 0.05)`;
      textColor = '#1f2937';
      break;
    case 'dark':
      bgColor = `rgba(${hexToRgb(baseColor)}, 0.2)`;
      textColor = '#ffffff';
      break;
    default:
      bgColor = `rgba(${hexToRgb(baseColor)}, 0.3)`;
      textColor = '#ffffff';
      break;
  }

  return {
    background: `linear-gradient(135deg, ${bgColor}, rgba(255,255,255,0.1))`,
    color: textColor,
    border: `2px solid ${baseColor}`,
  };
});

// 工具函数：十六进制转RGB（避免按位运算）
function hexToRgb(hex: string): string {
  const cleaned = hex.replace('#', '');
  let rStr = '';
  let gStr = '';
  let bStr = '';

  if (cleaned.length === 3) {
    rStr = cleaned[0] + cleaned[0];
    gStr = cleaned[1] + cleaned[1];
    bStr = cleaned[2] + cleaned[2];
  } else {
    rStr = cleaned.substring(0, 2);
    gStr = cleaned.substring(2, 4);
    bStr = cleaned.substring(4, 6);
  }

  const r = parseInt(rStr, 16);
  const g = parseInt(gStr, 16);
  const b = parseInt(bStr, 16);
  return `${r}, ${g}, ${b}`;
}

// 应用主题
const applyTheme = async () => {
  const themeId = `${selectedTone.value}-${selectedPalette.value}`;
  try {
    await themeStore.switchTheme(themeId);
  } catch (err) {
    console.error('applyTheme failed', err);
  }
  console.log(`🎨 应用主题: ${themeId}`);
};
</script>

<style scoped lang="scss">
.color-palette-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.color-palette-popup {
  background: var(--bg-glass-popup);
  border: 2px solid var(--border-glass);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: var(--shadow-strong);
}

.palette-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid var(--border-glass);

  h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--page-text-primary);
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 32px;
    color: var(--page-text-secondary);
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;

    &:hover {
      background: var(--bg-glass-hover);
      color: var(--page-text-primary);
    }
  }
}

.palette-content {
  padding: 30px;
}

.tone-section,
.palette-section,
.preview-section {
  margin-bottom: 30px;

  h4 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--page-text-primary);
  }
}

.tone-options {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.tone-btn {
  padding: 10px 20px;
  border: 2px solid var(--border-glass);
  border-radius: 25px;
  background: var(--bg-glass);
  color: var(--page-text-primary);
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: var(--bg-glass-hover);
    border-color: var(--primary-color);
  }

  &.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--on-primary-text);
  }
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.color-item {
  position: relative;
  height: 80px;
  border: 3px solid transparent;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }

  &.active {
    border-color: #ffffff;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  }

  .color-name {
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    white-space: nowrap;
  }
}

.preview-section {
  .preview-card {
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);

    .preview-text {
      margin-bottom: 10px;

      &.primary {
        font-size: 18px;
        font-weight: 600;
      }

      &.secondary {
        font-size: 14px;
        opacity: 0.8;
      }
    }

    .preview-button {
      padding: 8px 16px;
      border: 2px solid currentColor;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.1);
      color: inherit;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid var(--border-glass);

  button {
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .cancel-btn {
    background: var(--bg-glass);
    border: 2px solid var(--border-glass);
    color: var(--page-text-secondary);

    &:hover {
      background: var(--bg-glass-hover);
      color: var(--page-text-primary);
    }
  }

  .apply-btn {
    background: var(--primary-color);
    border: 2px solid var(--primary-color);
    color: var(--on-primary-text);

    &:hover {
      background: var(--primary-color-medium);
      transform: translateY(-2px);
      box-shadow: var(--shadow-accent);
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .color-palette-popup {
    width: 95%;
    margin: 20px;
  }

  .palette-content {
    padding: 20px;
  }

  .color-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 10px;
  }

  .color-item {
    height: 60px;

    .color-name {
      font-size: 10px;
    }
  }

  .tone-options {
    justify-content: center;
  }

  .action-buttons {
    flex-direction: column;

    button {
      width: 100%;
    }
  }
}
</style>
