<template>
  <div v-if="show" class="dialog-overlay">
    <div class="dialog-container edit-dialog">
      <div class="dialog-header">
        <div class="dialog-title">编辑提醒</div>
        <div class="dialog-close" @click="handleClose">
          <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
        </div>
      </div>

      <div class="dialog-content edit-content">
        <!-- 当前提醒信息显示 -->
        <div class="current-reminder-info">
          <div class="info-label">当前提醒内容：</div>
          <div class="info-content">
            {{ reminderData?.display_text || '暂无提醒内容' }}
          </div>
        </div>

        <!-- 自然语言编辑区域 -->
        <div class="natural-edit-section">
          <!-- 输入提示 -->
          <div class="input-hint">
            <div class="hint-title">自然语言编辑</div>
            <div class="hint-desc">用自然语言描述如何修改这个提醒，例如：</div>
            <div class="hint-examples">
              <div class="example-item">• "改成明天下午2点，提前1小时提醒"</div>
              <div class="example-item">• "推迟到下周三"</div>
            </div>
          </div>

          <!-- 文字输入框 -->
          <div class="input-group">
            <label class="input-label">编辑指令</label>
            <div class="input-content-wrapper">
              <textarea
                ref="textInputRef"
                v-model="inputText"
                class="input-field natural-input"
                :placeholder="
                  isRecording ? '我在听，请说...' : '请用自然语言描述如何修改这个提醒...'
                "
                maxlength="100"
                rows="3"
              ></textarea>
              <!-- 麦克风按钮在输入框内部右侧 -->
              <div class="voice-toggle-inner" :class="{ breathing: isRecording }" @click="handleVoiceButtonClick">
                <MicrophoneIcon :size="16" />
              </div>
            </div>
            <div class="char-count">{{ inputText.length }}/100</div>
          </div>

          <!-- 提交按钮 -->
          <div class="submit-area">
            <button
              class="send-btn"
              :class="{ 'not-input': !inputText.trim() || isLoading }"
              :disabled="!inputText.trim() || isLoading"
              @click="handleSubmit"
            >
              <span v-if="isLoading">处理中...</span>
              <span v-else>确认修改</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount, nextTick } from 'vue';
import { showSuccessToast, showToast } from 'vant';
import { type IReminder, updateReminderNatural } from '@/apis/memory';
import { getStreamAsr } from '@/apis/chat';
import Recorder from 'recorder-realtime';
import { debounce } from 'lodash-es';
import { generateRandomString } from '@/utils';
import MicrophoneIcon from '@/assets/icons/MicrophoneIcon.vue';

interface IProps {
  show: boolean;
  userId: string;
  reminderData: IReminder | null;
}

const props = defineProps<IProps>();

const emit = defineEmits<{
  close: [];
}>();

// 状态管理
const inputText = ref('');
const isLoading = ref(false);
const textInputRef = ref(); // 输入框引用

// 语音输入相关状态
const isRecording = ref(false);
const recognizedText = ref('');
const micPermission = ref(false);
const audioBufferIndex = ref(0);
const sessionId = ref('');
const lastBuffer = ref<ArrayBuffer | null>(null);
const lastVoiceText = ref(''); // 上次语音识别的文字，用于增量更新

// 录音相关变量
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
let mediaStream: MediaStream | null = null;
let timerId: NodeJS.Timeout | null = null;

// 监听弹窗显示状态，重置输入内容
watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      inputText.value = '';
      recognizedText.value = '';
      if (isRecording.value) {
        void stopVoiceRecording();
      }
    }
  },
);

// 处理关闭
const handleClose = () => {
  if (isRecording.value) {
    void stopVoiceRecording();
  }
  emit('close');
};

// 在光标位置插入文字的工具函数（针对textarea元素）
const insertTextAtCursor = (newText: string) => {
  if (!textInputRef.value) return;

  const inputElement = textInputRef.value;
  const start = (inputElement.selectionStart as number) || 0;
  const end = (inputElement.selectionEnd as number) || 0;
  const currentValue = inputText.value;

  // 在光标位置插入新文字
  const newValue = currentValue.slice(0, start) + newText + currentValue.slice(end);
  inputText.value = newValue;

  // 更新光标位置到插入文字的末尾
  const newCursorPosition = start + newText.length;

  // 使用 nextTick 确保 DOM 更新后再设置光标位置
  void nextTick(() => {
    inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
    inputElement.focus();
  });
};

// 处理提交
const handleSubmit = () => {
  if (!inputText.value.trim() || isLoading.value || !props.reminderData || !props.userId) {
    return;
  }

  isLoading.value = true;

  // 立即关闭弹窗，给用户快速反馈
  emit('close');

  // 在后台继续API调用，不阻塞UI
  // 使用setTimeout确保API调用不会因为组件销毁而中断
  setTimeout(async () => {
    try {
      // 再次检查reminderData是否存在，确保安全
      if (!props.reminderData) {
        console.warn('⚠️ [EditReminderDialog] reminderData为空，跳过API调用');
        return;
      }

      const response = await updateReminderNatural({
        user_id: props.userId,
        reminder_id: props.reminderData.reminder_id,
        natural_text: inputText.value.trim(),
      });

      console.log('📡 [EditReminderDialog] 提醒修改响应:', response);

      if (response && response.result === 'success') {
        console.log('✅ [EditReminderDialog] 提醒修改成功');
        showSuccessToast('提醒修改成功');

        // 延迟1000ms后发送自定义事件通知刷新ReminderSection
        setTimeout(() => {
          window.dispatchEvent(
            new CustomEvent('editremindersuccess', {
              detail: {
                userId: props.userId,
                reminderId: props.reminderData?.reminder_id,
              },
            }),
          );
        }, 1000);
      } else {
        console.warn('⚠️ [EditReminderDialog] 修改提醒失败:', response);
        showToast('提醒修改失败，请重试');
      }
    } catch (error) {
      console.error('编辑提醒失败:', error);
      showToast('提醒修改失败，请重试');
    } finally {
      isLoading.value = false;
    }
  }, 100); // 延迟100ms执行，确保弹窗关闭流程不受影响
};

// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => {
      track.stop();
      console.log('🎤 [EditReminderDialog] 释放麦克风轨道:', track.kind);
    });
    mediaStream = null;
    micPermission.value = false;
    console.log('✅ [EditReminderDialog] 麦克风资源已释放');
  }
};

// 设置麦克风权限
const setMicPermission = async () => {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
};

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
    return;
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    void stopVoiceRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      // 修复：检查 full_text 而不是 text，并且确保 full_text 不为空且与当前值不同
      if (
        streamData.data.full_text &&
        streamData.data.full_text.trim() !== '' &&
        streamData.data.full_text !== lastVoiceText.value
      ) {
        // 计算新增的文字部分
        const newText = streamData.data.full_text;
        const previousText = lastVoiceText.value;

        // 如果新文字包含之前的文字，只插入新增部分
        let textToInsert = newText;
        if (previousText && newText.startsWith(previousText)) {
          textToInsert = newText.slice(previousText.length);
        }

        // 在光标位置插入新文字
        if (textToInsert) {
          insertTextAtCursor(textToInsert);
        }

        lastVoiceText.value = newText;
        recognizedText.value = newText;
        await autoSendTimeout();
      }
    }
  };
};

// 两秒不说话自动停止录音
const autoSendTimeout = debounce(async () => {
  await stopVoiceRecording();
}, 2000);

// 处理语音按钮点击 - 参考inputBar逻辑
const handleVoiceButtonClick = async () => {
  if (isRecording.value) {
    await stopVoiceRecording();
  } else {
    await startVoiceRecording();
  }
};

// 开始语音录音
const startVoiceRecording = async () => {
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    recognizedText.value = '';
    // 重置语音识别状态
    lastVoiceText.value = '';
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopVoiceRecording();
    }, 1000 * 60);
  }
};

// 停止语音录音
const stopVoiceRecording = async () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });

  if (recognizedText.value) {
    // 语音识别完成，文字已经通过 insertTextAtCursor 插入到光标位置
    // 不需要再次设置 inputText.value，避免覆盖用户可能的编辑
    console.log(
      '📤 [EditReminderDialog] 语音识别完成，文字已插入到光标位置:',
      recognizedText.value,
    );
  } else {
    showToast('录音解析为空，请重新录制~');
  }
  // 清空语音消息和上次识别文字
  recognizedText.value = '';
  lastVoiceText.value = '';
};

// 组件销毁时清理资源
onBeforeUnmount(() => {
  if (isRecording.value) {
    void stopVoiceRecording();
  }
  releaseMicrophoneResources();
});
</script>

<style scoped lang="scss">
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-dark);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: var(--bg-glass);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 600px;
  color: var(--text-primary);
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;

  &.edit-dialog {
    max-height: 90vh;
    overflow-y: auto;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 13px;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: var(--primary-color);
    font-size: 36px; // 增加4px (原来32px)
    font-weight: 600;
    flex-shrink: 0;
  }

  .dialog-close {
    background: var(--bg-glass);
    border: 1px solid var(--bg-glass-hover);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: var(--bg-glass-hover);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.dialog-content {
  flex: 1;
  overflow-y: auto;

  &.edit-content {
    padding-top: 0;
  }
}

// 当前提醒信息
.current-reminder-info {
  background: var(--primary-color-light);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  max-height: 300px;
  overflow-y: auto;

  .info-label {
    color: var(--primary-color);
    font-size: 32px; // 增加4px (原来28px)
    font-weight: 500;
    margin-bottom: 12px;
  }

  .info-content {
    color: var(--person-detail-context);
    font-size: 28px; // 增加4px (原来28px)
    font-weight: 400;
    line-height: 1.4;
    padding: 16px;
    border-radius: 8px;
  }
}

// 自然语言编辑区域
.natural-edit-section {
  .input-hint {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border-left: 4px solid var(--accent-color);
    box-shadow: var(--shadow-accent);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 32px;
    max-height: 300px;
    overflow-y: auto;

    .hint-title {
      color: var(--primary-color);
      font-size: 32px; // 增加4px (原来28px)
      font-weight: 600;
      margin-bottom: 12px;
    }

    .hint-desc {
      color: var(--person-detail-context);
      font-size: 28px; // 增加4px (原来24px)
      margin-bottom: 16px;
    }

    .hint-examples {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .example-item {
        color: var(--person-detail-context);
        font-size: 28px; // 增加4px (原来22px)
      }
    }
  }

  // 语音识别文字显示
  .voice-text-display {
    background: var(--primary-color-light);
    border: 2px solid var(--primary-color);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;

    .voice-placeholder {
      color: var(--person-detail-context);
      font-size: 28px; // 增加4px (原来24px)
      font-style: italic;
    }

    .voice-message-text {
      color: var(--text-primary);
      font-size: 28px; // 增加4px (原来24px)
      line-height: 1.5;
      text-align: center;
    }
  }

  // 输入组
  .input-group {
    margin-bottom: 32px;
    position: relative;

    .input-label {
      display: block;
      color: var(--primary-color);
      font-size: 32px; // 增加4px (原来28px)
      font-weight: 500;
      margin-bottom: 12px;
    }

    .input-content-wrapper {
      width: 100%;
      position: relative;
      display: flex;
      align-items: stretch;

      .voice-toggle-inner {
        position: absolute;
        right: 16px;
        top: 24px;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 50%;
        background: var(--bg-glass);
        border: 2px solid var(--primary-color);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
        z-index: 10;

        &.breathing {
          animation: breathing 2s ease-in-out infinite;
        }

        .iconfont {
          font-size: 28px;
          color: var(--primary-color);
        }
      }
    }

    .input-field {
      width: 100%;
      background: var(--bg-glass);
      border: 2px solid var(--bg-glass-hover);
      border-radius: 12px;
      padding: 16px 90px 16px 20px; // 右侧留出空间给麦克风按钮
      color: var(--person-detail-context);
      font-size: 28px; // 增加4px (原来26px)
      outline: none;
      transition: all 0.3s ease;
      box-sizing: border-box;

      &::placeholder {
        color: var(--person-detail-context);
      }

      &:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 4px var(--primary-color-medium);
      }

      &.natural-input {
        resize: vertical;
        min-height: 120px;
        line-height: 1.5;
      }
    }

    .char-count {
      position: absolute;
      bottom: 12px;
      right: 16px;
      color: var(--person-detail-context);
      font-size: 24px; // 增加4px (原来20px)
      pointer-events: none;
    }
  }

  // 提交按钮区域
  .submit-area {
    margin-top: 32px;
    display: flex;
    justify-content: center;

    .send-btn {
      flex: 1;
      padding: 16px 16px;
      border-radius: 20px;
      font-size: 36px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid var(--primary-color);
      background: var(--bg-glass);
      backdrop-filter: blur(10px);
      color: var(--primary-color);
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 200px;

      &:hover:not(.not-input) {
        background: var(--primary-color-medium);
        transform: translateY(-2px);
        box-shadow: var(--shadow-accent);
      }

      &.not-input {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes breathing {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 var(--primary-color-strong);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 8px var(--primary-color-medium);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 var(--primary-color-strong);
  }
}
</style>
