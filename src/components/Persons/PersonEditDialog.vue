<template>
  <div class="person-edit-dialog-overlay">
    <div class="dialog-container edit-dialog">
      <div class="dialog-header">
        <div class="dialog-title">编辑人员信息</div>
        <div class="dialog-close" @click="handleClose">
          <DeleteIcon :size="24" color="var(--primary-color)" />
        </div>
      </div>
      <div class="dialog-content edit-content">
        <!-- 头像和称呼组合区域 -->
        <div class="input-group avatar-name-group">
          <!-- 标签行 -->
          <div class="labels-row">
            <label class="input-label avatar-label">头像</label>
            <label class="input-label name-label">称呼</label>
          </div>
          <!-- 内容行 -->
          <div class="content-row">
            <div class="avatar-section">
              <div class="avatar-upload-wrapper">
                <AvatarUpload
                  ref="avatarUploadRef"
                  v-model="editForm.avatar"
                  :size="50"
                  placeholder="上传头像"
                  :max-size="10"
                  @upload-success="onAvatarUploadSuccess"
                  @upload-error="onAvatarUploadError"
                  @show-avatar-selection="showAvatarSelectionDialog = true"
                />
              </div>
            </div>
            <div class="name-section">
              <div class="input-wrapper">
                <!-- 语音按钮在输入框内部右侧 -->
                <div
                  class="voice-toggle-inner"
                  :class="{
                    breathing: isRecording && currentRecordingField === 'name',
                  }"
                  @click="() => handleVoiceButtonClick('name')"
                >
                  <MicrophoneIcon :size="16" />
                </div>
                <input
                  ref="nameInputRef"
                  v-model="editForm.canonical_name"
                  type="text"
                  class="input-field with-voice"
                  placeholder="请输入您的称呼"
                  maxlength="50"
                  @input="onEditFormChange"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="input-group">
          <label class="input-label">别名</label>
          <!-- 别名标签展示区域 -->
          <div v-if="aliasList.length > 0" class="alias-tags-container">
            <div v-for="(alias, index) in aliasList" :key="`alias-${index}`" class="alias-tag">
              <span class="alias-text">{{ alias }}</span>
              <button class="alias-remove-btn" type="button" @click="removeAlias(index)">
                <DeleteIcon :size="14" color="var(--primary-color)" />
              </button>
            </div>
          </div>
          <!-- 别名输入区域 -->
          <div class="alias-input-container">
            <div class="input-wrapper">
              <!-- 语音按钮在输入框内部右侧 -->
              <div
                class="voice-toggle-inner"
                :class="{
                  breathing: isRecording && currentRecordingField === 'alias',
                }"
                @click="() => handleVoiceButtonClick('alias')"
              >
                <MicrophoneIcon :size="16" />
              </div>
              <input
                ref="aliasInputRef"
                v-model="tempAliasInput"
                type="text"
                class="input-field with-voice"
                placeholder="请输入别名"
                maxlength="100"
                @focus="handleAliasInputFocus"
                @blur="handleAliasInputBlur"
                @keyup.enter="saveAlias"
              />
            </div>
            <!-- 保存/取消按钮 -->
            <div v-if="isAliasInputFocused" class="alias-buttons">
              <button
                class="alias-save-btn"
                type="button"
                :disabled="!tempAliasInput.trim()"
                @click="saveAlias"
              >
                保存
              </button>
              <button class="alias-cancel-btn" type="button" @click="cancelAliasInput">取消</button>
            </div>
          </div>
        </div>
        <div class="input-group">
          <label class="input-label">个人简介</label>
          <div class="textarea-wrapper">
            <textarea
              ref="summaryInputRef"
              v-model="editForm.profile_summary"
              class="textarea-field with-voice"
              placeholder="请输入个人简介"
              rows="3"
              maxlength="500"
              @input="onEditFormChange"
            ></textarea>
            <!-- 语音按钮在多行输入框内部右上角 -->
            <div
              class="voice-toggle-inner textarea-voice"
              :class="{
                breathing: isRecording && currentRecordingField === 'summary',
              }"
              @click="() => handleVoiceButtonClick('summary')"
            >
              <MicrophoneIcon :size="16" />
            </div>
          </div>
        </div>
        <div class="input-group">
          <label class="input-label">快速添加</label>
          <div class="key-attributes-container">
            <div v-for="(_value, key) in editForm.key_attributes" :key="key" class="attribute-item">
              <input :value="key" type="text" class="attribute-key" :placeholder="key" readonly />
              <div class="input-wrapper attribute-value-wrapper">
                <!-- 语音按钮在属性值输入框内部右侧 -->
                <div
                  class="voice-toggle-inner"
                  :class="{
                    breathing: isRecording && currentRecordingField === `attr-${key}`,
                  }"
                  @click="() => handleVoiceButtonClick(`attr-${key}`)"
                >
                  <MicrophoneIcon :size="16" />
                </div>
                <input
                  :ref="(el) => setAttributeInputRef(`attr-${key}`, el)"
                  v-model="editForm.key_attributes[key]"
                  type="text"
                  class="attribute-value with-voice"
                  :placeholder="`请输入${key}`"
                  maxlength="100"
                  @input="onEditFormChange"
                />
              </div>
              <button class="remove-attribute-btn" @click="removeExistingAttribute(key)">
                <DeleteIcon :size="14" color="var(--primary-color)" />
              </button>
            </div>
            <!-- 新属性输入框 -->
            <div v-for="attr in newAttributes" :key="attr.id" class="add-attribute-container">
              <input
                v-model="attr.key"
                type="text"
                class="attribute-key"
                placeholder="属性名称"
                maxlength="20"
                @input="onEditFormChange"
              />
              <div class="input-wrapper attribute-value-wrapper">
                <!-- 语音按钮在新属性值输入框内部右侧 -->
                <div
                  class="voice-toggle-inner"
                  :class="{
                    breathing: isRecording && currentRecordingField === `new-attr-${attr.id}`,
                  }"
                  @click="() => handleVoiceButtonClick(`new-attr-${attr.id}`)"
                >
                  <MicrophoneIcon :size="16" />
                </div>
                <input
                  :ref="(el) => setAttributeInputRef(`new-attr-${attr.id}`, el)"
                  v-model="attr.value"
                  type="text"
                  class="attribute-value with-voice"
                  placeholder="属性值"
                  maxlength="100"
                  @input="onEditFormChange"
                />
              </div>
              <button class="remove-attribute-btn" @click="cancelAddAttribute(attr.id)">
                <DeleteIcon :size="14" color="var(--primary-color)" />
              </button>
            </div>
            <!-- 添加属性按钮 -->
            <div class="add-attribute-btn-container">
              <button class="add-attribute-btn" @click="showAddAttributeInput">+ 添加属性</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="dialog-footer">
        <button class="cancel-btn" :disabled="isUpdating" @click="handleCancel">取消</button>
        <button
          class="confirm-btn"
          :disabled="isUpdating || !isFormValid"
          @click="handleConfirmSave"
        >
          <span v-if="isUpdating">保存中...</span>
          <span v-else>确认修改</span>
        </button>
      </div>
    </div>

    <!-- 头像选择弹窗 -->
    <AvatarSelectionDialog
      v-if="showAvatarSelectionDialog"
      @close="showAvatarSelectionDialog = false"
      @select-avatar="handleAvatarSelect"
      @upload-avatar="handleUploadAvatar"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onBeforeUnmount, nextTick } from 'vue';
import { updatePerson, type IPersonData } from '@/apis/relation';
import { showFailToast, showSuccessToast, showToast } from 'vant';
import AvatarUpload from '@/components/Common/AvatarUpload.vue';
import AvatarSelectionDialog from '@/components/Common/AvatarSelectionDialog.vue';
import { getStreamAsr } from '@/apis/chat';
import Recorder from 'recorder-realtime';
import { generateRandomString } from '@/utils';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';
import MicrophoneIcon from '@/assets/icons/MicrophoneIcon.vue';

// Props定义
interface IProps {
  person: IPersonData | null;
  userId: string;
  isUserProfile?: boolean; // 新增：标识是否为用户档案（核心节点）
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  success: [];
}>();

// 响应式数据
const isUpdating = ref(false);
const showAvatarSelectionDialog = ref(false);
const avatarUploadRef = ref();
const hasUnsavedChanges = ref(false);

// 别名相关状态
const isAliasInputFocused = ref(false);
const tempAliasInput = ref('');
const aliasList = ref<string[]>([]);

// 编辑表单数据
const editForm = ref({
  canonical_name: '',
  relationships: [] as string[],
  profile_summary: '',
  key_attributes: {} as Record<string, string>,
  avatar: '',
});

// 新属性相关数据 - 支持多个新属性输入框
const newAttributes = ref<Array<{ id: string; key: string; value: string }>>([]);

// 语音相关数据
const micPermission = ref(false); // 麦克风权限
const sessionId = ref(''); // 语音转文字sessionId
const audioBufferIndex = ref(0); // 语音转文字流序列号
const lastBuffer = ref(); // 语音转文字最后一条流
const voiceMessage = ref(''); // 发送的对话文字
const isRecording = ref(false); // 是否录音输入
const lastVoiceText = ref(''); // 上次语音识别的文字，用于增量更新
const currentRecordingField = ref(''); // 当前正在录音的字段

// 录音相关变量
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let timerId: ReturnType<typeof setTimeout> | null = null;
// 保存媒体流引用，用于释放麦克风资源
let mediaStream: MediaStream | null = null;

// 输入框引用
const nameInputRef = ref();
const aliasInputRef = ref();
const summaryInputRef = ref();
const attributeInputRefs = ref<Record<string, HTMLInputElement>>({});

// 默认关键属性键列表 - 与PersonList保持一致
const defaultAttributeKeys = [
  '居住地',
  '性别',
  '生日',
  '联系方式',
  '兴趣',
  '期望',
  '旅游历史',
  '饮食偏好',
  '过往历史',
];

// 初始化默认关键属性
const initializeDefaultAttributes = () => {
  const defaultAttributes: Record<string, string> = {};
  defaultAttributeKeys.forEach((key) => {
    defaultAttributes[key] = '';
  });
  return defaultAttributes;
};

// 表单验证 - 检查必填字段
const isFormValid = computed(() => {
  return editForm.value.canonical_name.trim().length > 0;
});

// 生成唯一ID
const generateId = () => {
  return Date.now().toString() + Math.random().toString(36).substring(2, 11);
};

// 设置属性输入框引用
const setAttributeInputRef = (key: string, el: unknown) => {
  if (el && el instanceof HTMLInputElement) {
    attributeInputRefs.value[key] = el;
  } else {
    delete attributeInputRefs.value[key];
  }
};

// 显示新属性输入框
const showAddAttributeInput = () => {
  newAttributes.value.push({
    id: generateId(),
    key: '',
    value: '',
  });
};

// 取消添加新属性
const cancelAddAttribute = (id: string) => {
  newAttributes.value = newAttributes.value.filter((a) => a.id !== id);
};

// 删除已有属性
const removeExistingAttribute = (key: string) => {
  // 从editForm.key_attributes中删除该属性
  delete editForm.value.key_attributes[key];
  // 触发表单变更
  onEditFormChange();
};

// 别名管理方法
const handleAliasInputFocus = () => {
  isAliasInputFocused.value = true;
};

const handleAliasInputBlur = () => {
  // 延迟隐藏按钮，以便点击按钮事件能够正常触发
  setTimeout(() => {
    isAliasInputFocused.value = false;
  }, 150);
};

const saveAlias = () => {
  const alias = tempAliasInput.value.trim();
  if (alias && !aliasList.value.includes(alias)) {
    aliasList.value.push(alias);
    tempAliasInput.value = '';
    // 触发表单变更
    onEditFormChange();
  }
  isAliasInputFocused.value = false;
};

const cancelAliasInput = () => {
  tempAliasInput.value = '';
  isAliasInputFocused.value = false;
};

const removeAlias = (index: number) => {
  aliasList.value.splice(index, 1);
  // 触发表单变更
  onEditFormChange();
};

// 编辑表单内容更改时的处理函数
const onEditFormChange = () => {
  // 标记有未保存的更改
  hasUnsavedChanges.value = true;
};

// 头像上传成功处理
const onAvatarUploadSuccess = (url: string) => {
  console.log('✅ [PersonEditDialog] 头像上传成功:', url);
  editForm.value.avatar = url;
  // 不再自动保存，等待用户点击确认修改按钮
};

// 头像上传失败处理
const onAvatarUploadError = (error: string) => {
  console.error('❌ [PersonEditDialog] 头像上传失败:', error);
};

// 处理头像选择
const handleAvatarSelect = (selectedAvatarId: string) => {
  console.log('✅ [PersonEditDialog] 选择默认头像:', selectedAvatarId);
  editForm.value.avatar = selectedAvatarId;
  showAvatarSelectionDialog.value = false;
  // 不再自动保存，等待用户点击确认修改按钮
};

// 处理上传头像
const handleUploadAvatar = () => {
  showAvatarSelectionDialog.value = false;
  // 触发AvatarUpload组件的文件选择
  if (avatarUploadRef.value && avatarUploadRef.value.triggerUpload) {
    avatarUploadRef.value.triggerUpload();
  }
};

// 处理取消 - 直接关闭弹窗，不保存
const handleCancel = () => {
  emit('close');
};

// 处理弹窗关闭 - 直接关闭弹窗，不保存
const handleClose = () => {
  handleCancel();
};

// 处理确认保存
const handleConfirmSave = async () => {
  await submitEditForm();
};

// 语音相关方法
// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => track.stop());
    mediaStream = null;
  }
};

// 设置麦克风权限（仅在需要时请求）
async function setMicPermission() {
  try {
    // 保存媒体流引用，用于后续释放资源
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
}

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    // 如果浏览器不支持录音功能，给用户提示
    showToast('录音失败，浏览器不支持录音功能');
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  // 当录音开始时的回调
  recorder.onstart = () => {};

  // 处理录音错误的回调
  recorder.onstreamerror = () => {
    // 显示录音错误消息并停止录音
    showToast('录音失败');
    cancelRecording();
  };

  // 获取可用数据的回调
  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      // 修复：检查 full_text 而不是 text，并且确保 full_text 不为空且与当前值不同
      if (
        streamData.data.full_text &&
        streamData.data.full_text.trim() !== '' &&
        streamData.data.full_text !== lastVoiceText.value
      ) {
        // 计算新增的文字部分
        const newText = streamData.data.full_text;
        const previousText = lastVoiceText.value;

        // 如果新文字包含之前的文字，只插入新增部分
        let textToInsert = newText;
        if (previousText && newText.startsWith(previousText)) {
          textToInsert = newText.slice(previousText.length);
        }

        // 在光标位置插入新文字
        if (textToInsert) {
          insertTextAtCursor(textToInsert);
        }

        lastVoiceText.value = newText;
        voiceMessage.value = newText;
      }
    }
  };
};

// 提交编辑表单
const submitEditForm = async () => {
  if (!props.person || !editForm.value.canonical_name.trim() || isUpdating.value) {
    return;
  }

  try {
    isUpdating.value = true;
    console.log('🔄 [PersonEditDialog.vue] 提交编辑表单...', {
      userId: props.userId,
      personId: props.person.person_id,
      editForm: editForm.value,
    });

    // 合并新属性到key_attributes
    const finalKeyAttributes: Record<string, string> = {};

    // 处理默认属性：只有当 attribute-value 不为空时才保存
    Object.entries(editForm.value.key_attributes).forEach(([key, value]) => {
      if (value && value.trim()) {
        finalKeyAttributes[key] = value.trim();
      }
    });

    // 处理新增属性：只有当 attribute-key 不为空时才保存新增的属性
    // 当 attribute-key 为空，attribute-value 不为空时，不保存
    // 当 attribute-key 不为空，attribute-value 为空时，保存
    newAttributes.value.forEach((attr) => {
      if (attr.key.trim()) {
        finalKeyAttributes[attr.key.trim()] = attr.value.trim();
      }
    });

    // 处理aliases字段，将别名数组转换为API需要的格式
    let submitAliases = '';
    if (aliasList.value.length === 0) {
      submitAliases = '[]';
    } else {
      // 将别名数组转换为带双引号的格式，然后用逗号连接，最后添加[]
      const aliasArray = aliasList.value
        .filter((alias) => alias.trim().length > 0)
        .map((alias) => `"${alias.trim()}"`);

      if (aliasArray.length > 0) {
        submitAliases = `[${aliasArray.join(',')}]`;
      } else {
        submitAliases = '[]';
      }
    }

    const response = await updatePerson(props.person.person_id, {
      user_id: props.userId,
      canonical_name: editForm.value.canonical_name.trim(),
      aliases: submitAliases,
      relationships: editForm.value.relationships,
      profile_summary: editForm.value.profile_summary.trim(),
      key_attributes: finalKeyAttributes,
      is_user: props.isUserProfile || false, // 根据是否为核心节点设置is_user参数
      avatar: editForm.value.avatar,
    });

    console.log('📡 [PersonEditDialog.vue] 更新人员响应:', response);

    if (response && response.result === 'success') {
      console.log('✅ [PersonEditDialog.vue] 人员更新成功');
      showSuccessToast(`与${editForm.value.canonical_name}的关系更新成功了喔～`);

      // 重置未保存状态
      hasUnsavedChanges.value = false;

      // 通知父组件更新成功
      emit('success');

      // 清空新属性数组，因为已经合并到 key_attributes 中
      newAttributes.value = [];

      // 关闭弹窗
      emit('close');
    } else {
      console.warn('⚠️ [PersonEditDialog.vue] 更新人员失败:', response);
      showFailToast('更新人员失败');
    }
  } catch (error) {
    console.error('❌ [PersonEditDialog.vue] 更新人员失败:', error);
    showFailToast('更新人员失败');
  } finally {
    isUpdating.value = false;
  }
};

// 取消录音
const cancelRecording = () => {
  if (isRecording.value) {
    isRecording.value = false;
    if (timerId !== null) {
      clearTimeout(timerId);
      timerId = null;
    }
    if (recorder) {
      recorder.stop();
    }
    // 释放麦克风资源
    releaseMicrophoneResources();
    // 清空语音消息和上次识别文字
    voiceMessage.value = '';
    lastVoiceText.value = '';
    currentRecordingField.value = '';
  }
};

// 开始录音
async function startRecording() {
  if (isRecording.value) {
    await stopRecording();
    return;
  }

  // 如果没有麦克风权限，先请求权限
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    // 重置语音识别状态
    lastVoiceText.value = '';
    voiceMessage.value = '';
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopRecording();
    }, 1000 * 60);
  }
}

// 结束录音
async function stopRecording() {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null; // 重置定时器 ID
  }
  if (recorder) {
    recorder.stop();
  }

  // 释放麦克风资源
  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });
  if (voiceMessage.value) {
    // 语音识别完成，文字已经通过 insertTextAtCursor 插入到光标位置
    console.log('📤 [PersonEditDialog] 语音识别完成，文字已插入到光标位置:', voiceMessage.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }
  // 清空语音消息和上次识别文字
  voiceMessage.value = '';
  lastVoiceText.value = '';
  currentRecordingField.value = '';
}

// 处理语音按钮点击 - 直接开始录音
const handleVoiceButtonClick = async (fieldName: string) => {
  currentRecordingField.value = fieldName;
  await startRecording();
};

// 在光标位置插入文字的工具函数
const insertTextAtCursor = (
  newText: string,
  targetRef?: { value: HTMLInputElement | HTMLTextAreaElement },
) => {
  // 如果没有指定目标引用，则根据当前焦点确定
  let inputElement: HTMLInputElement | HTMLTextAreaElement | null = null;
  let currentValue = '';
  let updateValue: (value: string) => void = () => {};

  if (targetRef) {
    // 使用指定的引用
    inputElement = targetRef.value;
    if (targetRef === nameInputRef.value) {
      currentValue = editForm.value.canonical_name;
      updateValue = (value: string) => {
        editForm.value.canonical_name = value;
      };
    } else if (targetRef === aliasInputRef.value) {
      currentValue = tempAliasInput.value;
      updateValue = (value: string) => {
        tempAliasInput.value = value;
      };
    } else if (targetRef === summaryInputRef.value) {
      currentValue = editForm.value.profile_summary;
      updateValue = (value: string) => {
        editForm.value.profile_summary = value;
      };
    }
  } else if (currentRecordingField.value === 'name') {
    inputElement = nameInputRef.value;
    currentValue = editForm.value.canonical_name;
    updateValue = (value: string) => {
      editForm.value.canonical_name = value;
    };
  } else if (currentRecordingField.value === 'alias') {
    inputElement = aliasInputRef.value;
    currentValue = tempAliasInput.value;
    updateValue = (value: string) => {
      tempAliasInput.value = value;
    };
  } else if (currentRecordingField.value === 'summary') {
    inputElement = summaryInputRef.value;
    currentValue = editForm.value.profile_summary;
    updateValue = (value: string) => {
      editForm.value.profile_summary = value;
    };
  } else if (currentRecordingField.value.startsWith('attr-')) {
    // 处理属性输入框
    const attrKey = currentRecordingField.value.replace('attr-', '');
    inputElement = attributeInputRefs.value[currentRecordingField.value];
    currentValue = editForm.value.key_attributes[attrKey] || '';
    updateValue = (value: string) => {
      editForm.value.key_attributes[attrKey] = value;
    };
  } else if (currentRecordingField.value.startsWith('new-attr-')) {
    // 处理新属性输入框
    const attrId = currentRecordingField.value.replace('new-attr-', '');
    inputElement = attributeInputRefs.value[currentRecordingField.value];
    const attr = newAttributes.value.find((a) => a.id === attrId);
    if (attr) {
      currentValue = attr.value;
      updateValue = (value: string) => {
        attr.value = value;
      };
    }
  }

  if (!inputElement) return;

  const start = (inputElement.selectionStart as number) || 0;
  const end = (inputElement.selectionEnd as number) || 0;

  // 在光标位置插入新文字
  const newValue = currentValue.slice(0, start) + newText + currentValue.slice(end);
  updateValue(newValue);

  // 更新光标位置到插入文字的末尾
  const newCursorPosition = start + newText.length;

  // 使用 nextTick 确保 DOM 更新后再设置光标位置
  void nextTick(() => {
    inputElement!.setSelectionRange(newCursorPosition, newCursorPosition);
    inputElement!.focus();
  });
};

// 组件卸载时释放麦克风资源
onBeforeUnmount(() => {
  console.log('🧹 [PersonEditDialog] 组件卸载，释放麦克风资源');
  if (isRecording.value) {
    // 如果正在录音，先停止录音
    if (recorder) {
      recorder.stop();
    }
    isRecording.value = false;
  }
  // 释放麦克风资源
  releaseMicrophoneResources();
});

// 监听person变化，初始化表单数据
watch(
  () => props.person,
  (newPerson) => {
    if (newPerson) {
      // 初始化默认属性，然后合并现有属性值
      const keyAttributes: Record<string, string> = initializeDefaultAttributes();

      if (newPerson.key_attributes) {
        Object.entries(newPerson.key_attributes).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            keyAttributes[key] = String(value);
          }
        });
      }

      // 处理relationships字段，确保它是数组格式
      let relationships: string[] = [];
      if (newPerson.relationships) {
        if (typeof newPerson.relationships === 'string') {
          try {
            relationships = JSON.parse(newPerson.relationships);
          } catch (parseError) {
            console.warn('⚠️ [PersonEditDialog.vue] relationships JSON解析失败:', parseError);
            relationships = [];
          }
        } else {
          relationships = newPerson.relationships;
        }
      }

      // 处理aliases字段，转换为别名数组
      aliasList.value = [];
      const aliasesData = newPerson.aliases || '';
      if (aliasesData !== '[]' && aliasesData.startsWith('[') && aliasesData.endsWith(']')) {
        // 去除首尾的[]
        const aliasesContent = aliasesData.replace(/^\[|\]$/g, '');
        if (aliasesContent.trim()) {
          // 按逗号分割，去除每项的双引号和空格
          aliasList.value = aliasesContent
            .split(',')
            .map((alias) => alias.trim().replace(/^"|"$/g, ''))
            .filter((alias) => alias.length > 0);
        }
      }

      editForm.value = {
        canonical_name: newPerson.canonical_name,
        relationships,
        profile_summary: newPerson.profile_summary || '',
        key_attributes: keyAttributes,
        avatar: newPerson.avatar || '',
      };

      // 重置新属性数组
      newAttributes.value = [];

      // 重置别名相关状态
      tempAliasInput.value = '';
      isAliasInputFocused.value = false;

      // 重置未保存状态
      hasUnsavedChanges.value = false;
    }
  },
  { immediate: true },
);
</script>

<style lang="scss" scoped>
// 对话框样式
.person-edit-dialog-overlay {
  position: absolute; // 改为absolute定位，相对于父容器
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent; // 使用主题变量
  backdrop-filter: blur(20px); // 添加毛玻璃模糊效果
  display: flex;
  align-items: stretch; // 改为拉伸对齐
  justify-content: stretch; // 改为拉伸对齐
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
  pointer-events: auto; // 确保可以接收点击事件
}

.dialog-container {
  position: relative; // 添加相对定位，为dialog-footer提供定位基准
  background: var(--bg-glass); // 使用弹窗专用背景变量
  border: 1px solid var(--border-glass); // 使用主题变量
  border-radius: 20px; // 添加圆角
  padding: 30px;
  padding-bottom: 120px; // 增加底部padding，为固定的dialog-footer留空间
  box-sizing: border-box;
  backdrop-filter: blur(15px); // 添加毛玻璃模糊效果
  border-left: none; // 移除左边框
  box-shadow: var(--shadow-strong); // 使用主题变量
  transition: all 0.3s ease;
  width: 100%; // 改为100%宽度
  height: calc(100% - 150px); // 高度占据父容器减去inputbar空间，因为inputbar在页面内部
  color: var(--text-primary); // 使用主题变量
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止整个容器滚动

  &.edit-dialog {
    max-width: none; // 移除最大宽度限制
    height: calc(100% - 150px); // 使用计算高度
    overflow-y: auto;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 13px;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: var(--primary-color);
    font-size: 40px; // 增加8px (原来32px)
    font-weight: 600;
  }

  .dialog-close {
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: var(--bg-glass-hover);
      border-color: var(--border-accent);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 32px;

  &.edit-content {
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }
  }

  .input-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    border: none;
    border-radius: 16px;
    padding: 22px; // 增加2px上下padding (原来20px)
    background: var(--bg-glass); // 使用主题变量
    backdrop-filter: blur(10px);
    border-left: 4px solid var(--accent-color);
    box-shadow: var(--shadow-accent);
    transition: all 0.3s ease;

    &.avatar-name-group {
      .labels-row {
        display: flex;
        gap: 24px;
        margin-bottom: 12px;

        .avatar-label {
          width: 120px;
          text-align: left;
          flex-shrink: 0;
          padding-left: 18px;
        }

        .name-label {
          flex: 1;
        }
      }

      .content-row {
        display: flex;
        gap: 24px;
        align-items: flex-end;

        .avatar-section {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          flex-shrink: 0;
          width: 120px;

          .avatar-upload-wrapper {
            display: flex;
            justify-content: flex-start;
          }
        }

        .name-section {
          flex: 1;

          .input-field {
            width: 100%;
          }
        }
      }
    }

    .input-label {
      color: var(--primary-color);
      font-size: 30px; // 增加8px (原来22px)
      font-weight: 600;
      margin-bottom: 8px;
    }

    // 输入框包装器样式
    .input-wrapper {
      position: relative;
      width: 100%;
    }

    .textarea-wrapper {
      position: relative;
      width: 100%;
    }

    // 属性值包装器样式 - 确保语音按钮正确定位
    .attribute-value-wrapper {
      position: relative !important;
      width: 100%;

      // 确保语音按钮在属性值输入框中正确显示
      .voice-toggle-inner {
        position: absolute !important;
        right: 16px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        z-index: 20 !important; // 提高z-index确保显示在最上层
      }
    }

    // 语音按钮样式
    .voice-toggle-inner {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 2px solid var(--border-accent);
      backdrop-filter: blur(20px);
      transition: all 0.3s ease;
      z-index: 10;

      &.textarea-voice {
        top: 24px;
        transform: none;
      }

      .iconfont {
        font-size: 24px;
        color: var(--text-primary);
      }

      &:hover {
        background: var(--primary-color-medium);
        border-color: var(--primary-color);
        transform: translateY(-50%) scale(1.05);

        &.textarea-voice {
          transform: scale(1.05);
        }
      }
    }

    .input-field {
      width: 100%;
      background: var(--bg-input);
      border: 2px solid var(--border-accent);
      border-radius: 20px;
      padding: 18px 22px;
      color: var(--person-detail-context);
      font-size: 32px; // 增加8px (原来24px)
      line-height: 1.6;
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &.with-voice {
        padding-right: 76px; // 为语音按钮留出空间
      }

      &::placeholder {
        color: var(--person-detail-context);
      }

      &:focus {
        outline: none;
        border-color: var(--primary-color);
        background: var(--bg-input-focus);
        box-shadow: 0 0 0 3px var(--primary-color-light);
      }
    }

    // 别名相关样式
    .alias-tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-bottom: 16px;
    }

    .alias-tag {
      display: flex;
      align-items: center;
      background: var(--primary-color-light);
      border: 1px solid var(--primary-color);
      border-radius: 24px;
      padding: 8px 16px;
      font-size: 24px;
      color: var(--person-detail-context);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:hover {
        background: var(--primary-color-medium);
        border-color: var(--primary-color);
      }

      .alias-text {
        margin-right: 8px;
      }

      .alias-remove-btn {
        background: transparent;
        border: none;
        color: var(--text-tertiary);
        font-size: 20px;
        cursor: pointer;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &:hover {
          background: var(--bg-glass);
          color: var(--text-primary);
          transform: scale(1.1);
        }
      }
    }

    .alias-input-container {
      position: relative;
    }

    .alias-buttons {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
      margin-top: 16px;

      .alias-save-btn,
      .alias-cancel-btn {
        padding: 12px 20px;
        border-radius: 12px;
        font-size: 24px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        backdrop-filter: blur(10px);

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }

      .alias-save-btn {
        background: var(--primary-color-light);
        color: var(--primary-color);
        border-color: var(--primary-color);

        &:hover:not(:disabled) {
          background: var(--primary-color-medium);
          transform: translateY(-2px);
          box-shadow: var(--shadow-accent);
        }
      }

      .alias-cancel-btn {
        background: var(--bg-glass);
        color: var(--text-tertiary);
        border-color: var(--border-glass);

        &:hover {
          background: var(--bg-glass-hover);
          transform: translateY(-2px);
          box-shadow: var(--shadow-medium);
        }
      }
    }

    .textarea-field {
      width: 100%;
      background: var(--bg-input);
      border: 2px solid var(--border-accent);
      border-radius: 20px;
      padding: 18px 22px;
      color: var(--person-detail-context);
      font-size: 32px; // 增加8px (原来24px)
      line-height: 1.6;
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      resize: vertical;
      min-height: 100px;
      font-family: inherit;
      transition: all 0.3s ease;

      &.with-voice {
        padding-right: 76px; // 为语音按钮留出空间
        padding-top: 24px; // 为右上角的语音按钮留出空间
      }

      &::placeholder {
        color: var(--person-detail-context);
      }

      &:focus {
        outline: none;
        border-color: var(--primary-color);
        background: var(--bg-input-focus);
        box-shadow: 0 0 0 3px var(--primary-color-light);
      }
    }

    // 关键属性容器样式
    .key-attributes-container {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .attribute-item,
      .add-attribute-container {
        display: flex;
        gap: 12px;
        align-items: center;
        width: 100%;
        box-sizing: border-box;

        .attribute-value-wrapper {
          flex: 1;
          min-width: 0;
          position: relative; // 为语音按钮提供定位基准
        }
      }

      // 为新增属性容器提供更紧凑的布局
      .add-attribute-container {
        .attribute-value {
          flex: 1;
          min-width: 0; // 允许收缩
          max-width: calc(100% - 150px); // 为关闭按钮预留空间
        }
      }

      .attribute-item,
      .add-attribute-container {
        .attribute-key,
        .attribute-value {
          background: var(--bg-input);
          border: 2px solid var(--border-accent);
          border-radius: 12px;
          padding: 16px;
          font-size: 28px; // 增加8px (原来22px)
          line-height: 1.6;
          box-sizing: border-box;
          backdrop-filter: blur(10px);
          transition: all 0.2s ease;

          &.with-voice {
            padding-right: 100px; // 为语音按钮留出空间
          }

          &::placeholder {
            color: var(--person-detail-context);
          }

          &:focus {
            outline: none;
            background: var(--bg-input-focus);
            box-shadow: 0 0 0 2px var(--accent-color-strong);
          }

          &:read-only {
            background: var(--bg-glass);
            color: var(--text-disabled);
          }
        }

        .attribute-key {
          flex: 0 0 150px; // 固定宽度，防止过宽
          min-width: 150px;
          max-width: 150px;
          color: var(--person-detail-context) !important; // 属性键使用context颜色，覆盖read-only样式
        }

        .attribute-value {
          flex: 1;
          min-width: 0; // 允许收缩
          color: var(--person-detail-context); // 属性值使用context颜色
        }

        .remove-attribute-btn {
          background: transparent;
          border: none;
          border-radius: 50%;
          width: 36px;
          height: 36px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          color: var(--text-tertiary);
          transition: all 0.3s ease;
          font-size: 28px; // 增加8px (原来20px)
          flex-shrink: 0; // 防止被压缩
          margin-left: 8px; // 增加左边距，确保与输入框有间隔

          &:hover {
            background: var(--bg-glass);
            color: var(--text-primary);
            transform: scale(1.1);
          }
        }
      }

      .add-attribute-btn-container {
        margin-top: 16px;

        .add-attribute-btn {
          width: 100%;
          background: transparent;
          color: var(--primary-color);
          border: 2px solid var(--primary-color);
          border-radius: 20px;
          padding: 16px 16px;
          font-size: 36px; // 增加8px (原来28px)
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 60px;

          &:hover {
            background: var(--primary-color-light);
            transform: translateY(-2px);
            box-shadow: var(--shadow-accent);
          }
        }
      }
    }
  }
}

.dialog-footer {
  position: absolute; // 相对于弹窗容器定位
  bottom: 20px; // 距离弹窗底部20px，为inputbar留空间
  left: 30px;
  right: 30px;
  display: flex;
  gap: 20px;
  padding: 20px 30px;
  background: var(--bg-glass); // 使用主题变量
  backdrop-filter: blur(10px);
  border-radius: 16px;

  .cancel-btn,
  .confirm-btn {
    flex: 1;
    padding: 16px 16px;
    border-radius: 20px;
    font-size: 36px; // 增加8px (原来28px)
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    background: var(--bg-glass); // 使用主题变量
    backdrop-filter: blur(10px); // 添加背景模糊效果
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .cancel-btn {
    color: var(--text-tertiary);
    border-color: var(--border-glass);

    &:hover:not(:disabled) {
      background: var(--bg-glass-hover);
      transform: translateY(-2px);
      box-shadow: var(--shadow-medium);
    }
  }

  .confirm-btn {
    color: var(--primary-color);
    border-color: var(--primary-color);

    &:hover:not(:disabled) {
      background: var(--primary-color-medium);
      transform: translateY(-2px);
      box-shadow: var(--shadow-accent);
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// 普通语音按钮的呼吸动画（非textarea）
.voice-toggle-inner:not(.textarea-voice).breathing {
  animation: breathing-normal 2s ease-in-out infinite;
}

@keyframes breathing-normal {
  0%,
  100% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 0 var(--primary-color-strong);
  }
  50% {
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 0 0 10px transparent;
  }
}

// 为textarea语音按钮的呼吸动画
.voice-toggle-inner.textarea-voice.breathing {
  animation: breathing-textarea 2s ease-in-out infinite;
}

@keyframes breathing-textarea {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 var(--primary-color-strong);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px transparent;
  }
}
</style>
