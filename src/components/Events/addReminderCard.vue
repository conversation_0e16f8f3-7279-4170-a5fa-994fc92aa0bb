<template>
  <div class="add-reminder-card" @click="handleClick">
    <div class="add-reminder-content">
      <div class="add-icon">
        <PlusIcon class="add" :size="32" />
      </div>
      <div class="add-text">添加提醒</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import PlusIcon from '@/assets/icons/PlusIcon.vue';

const emit = defineEmits<{
  click: [];
}>();

const handleClick = () => {
  emit('click');
};
</script>

<style scoped lang="scss">
.add-reminder-card {
  width: 220px;
  height: 160px;
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  border-radius: 16px;
  padding: 20px;
  box-sizing: border-box;
  transition: all 0.3s ease;
  cursor: pointer;
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: var(--shadow-strong);
    border-color: var(--accent-color);
    background: var(--bg-glass-hover);
    border-style: solid;
  }

  &:active {
    transform: translateY(-4px) scale(1.02);
  }
}

.add-reminder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.add-icon {
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--accent-color-light);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.add {
  color: var(--text-primary);
  font-size: 32px; /* 增加8px (原来32px) */
  font-weight: bold;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 提升可读性：去除半透明与反转滤镜 */
}

.add-text {
  color: var(--text-primary);
  font-size: 28px; // 增加4px (原来16px)
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.add-reminder-card:hover {
  .add-icon {
    background: var(--accent-color-medium);
    transform: scale(1.1);

    .add-image {
      opacity: 1;
    }
  }

  .add-text {
    color: var(--accent-color);
    transform: translateY(-2px);
  }
}
</style>
