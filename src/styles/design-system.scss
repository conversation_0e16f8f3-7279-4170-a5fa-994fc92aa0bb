// 设计系统 - 统一的样式变量和类
// 基于赛博朋克/未来科技风格，针对移动端优化
// 支持动态主题切换

// ==================== 颜色系统 ====================
:root {
  // 主题背景图片 - 由主题系统动态设置
  --theme-background-image: url('@/assets/img/galaxy5.jpg');



  // 页面文字颜色 - 统一为纯黑，便于全局管理
  --page-text-primary: #000000;
  --page-text-secondary: #000000;
  --page-text-tertiary: #000000;

  // 主色调 - 由主题系统动态设置
  --primary-color: #00bcd4;
  --primary-color-light: rgba(0, 188, 212, 0.1);
  --primary-color-medium: rgba(0, 188, 212, 0.2);
  --primary-color-strong: rgba(0, 188, 212, 0.3);
  --primary-color-timestamp: rgba(0, 188, 212, 0.8);
  // 主色背景上的文字颜色（按钮等）统一为纯黑
  --on-primary-text: #000000;

  // 文本选中颜色 - 基于主色调
  --selection-bg: var(--primary-color-strong);
  --selection-color: #ffffff;

  // 强调色 - 由主题系统动态设置
  --accent-color: #00ffff;
  --accent-color-light: rgba(0, 255, 255, 0.1);
  --accent-color-medium: rgba(0, 255, 255, 0.2);
  --accent-color-strong: rgba(0, 255, 255, 0.3);

  // 文字颜色 - 全部统一为纯黑（无透明），提高可读性
  --text-primary: #000000;
  --text-secondary: #000000;
  --text-tertiary: #000000;
  --text-disabled: #000000;

  // 背景色 - 叠加主色渐变以增强主题氛围
  --bg-glass:
    linear-gradient(0deg, var(--primary-color-light), transparent 65%), rgba(1, 28, 32, 0.4);
  --bg-glass-hover:
    linear-gradient(0deg, var(--primary-color-medium), transparent 65%), rgba(1, 28, 32, 0.6);
  --bg-glass-popup:
    linear-gradient(0deg, var(--primary-color-light), transparent 65%), rgba(1, 28, 32, 0.4); // 弹窗专用背景，透明度更低
  --border-glass: rgba(255, 255, 255, 0.1);
  // 边框强调色改为随主题主色变化
  --border-accent: var(--primary-color-medium);

  // 阴影 - 恢复原来的霓虹发光效果
  --shadow-soft: 0 4px 12px rgba(255, 255, 255, 0.3);
  --shadow-strong: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05);
  --shadow-accent: -4px 0 8px rgba(0, 255, 255, 0.3);

  // 字体大小 (移动端优化)
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 22px;
  --font-size-2xl: 26px;
  --font-size-3xl: 32px;

  // 间距系统
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;

  // 边框圆角
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 20px;
  --border-radius-full: 50px;

  // 其他颜色 - 文本四级同样使用纯黑，避免发灰
  --text-quaternary: #000000;
  --border-light: rgba(255, 255, 255, 0.1);
  --bg-hover: rgba(255, 255, 255, 0.05);
  --bg-input: rgba(255, 255, 255, 0.05); // 恢复原来的输入框背景
  --bg-input-focus: rgba(255, 255, 255, 0.15); // 恢复原来的输入框聚焦背景
  --overlay-dark: rgba(0, 0, 0, 0.3); // 恢复原来的深色覆盖
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --success-color: #22c55e; // 由主题系统动态设置
  --success-color-light: rgba(34, 197, 94, 0.1); // 由主题系统动态设置
  --success-color-medium: rgba(34, 197, 94, 0.2); // 由主题系统动态设置
  --success-color-strong: rgba(34, 197, 94, 0.3); // 由主题系统动态设置
  --error-color: #ef4444; // 由主题系统动态设置
  --error-color-light: rgba(239, 68, 68, 0.1); // 由主题系统动态设置
  --error-color-medium: rgba(239, 68, 68, 0.2); // 由主题系统动态设置
  --error-color-strong: rgba(239, 68, 68, 0.3); // 由主题系统动态设置
}

// 全局文本选中样式
::selection {
  background-color: var(--selection-bg);
  color: var(--selection-color);
}

::-moz-selection {
  background-color: var(--selection-bg);
  color: var(--selection-color);
}

// ==================== 字体系统 (移动端优化) ====================

// 主标题 - 用于页面标题
.cyber-title {
  color: var(--page-text-primary);
  font-size: 42px;
  font-weight: 450;
  line-height: 1.4;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

// 副标题 - 用于页面副标题
.cyber-subtitle {
  color: var(--text-secondary);
  font-size: 42px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

// 功能标题 - 用于功能卡片标题
.cyber-feature-title {
  color: var(--text-primary);
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}

// 功能描述 - 用于功能卡片描述
.cyber-feature-desc {
  color: var(--text-tertiary);
  font-size: 24px;
  font-weight: 400;
  line-height: 1.5;
}

// 正文 - 用于一般内容
.cyber-body {
  color: var(--text-secondary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
}

// 辅助文字 - 用于次要信息
.cyber-caption {
  color: var(--text-tertiary);
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4;
}

// ==================== 组件系统 ====================

// 基础卡片
.cyber-card {
  /* 叠加一层基于主色的轻微渐变，让主题更明显 */
  background: linear-gradient(0deg, var(--primary-color-light), transparent 65%), var(--bg-glass);
  border: 2px solid var(--border-accent);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(20px);
  box-shadow:
    var(--shadow-strong),
    0 0 12px var(--primary-color-light),
    var(--shadow-accent);
  transition: all 0.3s ease;

  &:hover {
    background:
      linear-gradient(0deg, var(--primary-color-medium), transparent 65%), var(--bg-glass-hover);
    transform: translateY(-2px);
  }
}

// 功能卡片
.cyber-feature-card {
  display: flex;
  align-items: center;
  gap: 20px;
  background: rgba(113, 229, 247, 0.15); // 比container更浅的透明度（从0.6改为0.3）
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 16px;
  padding: 18px; // 增加内边距
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 0 8px rgba(0, 255, 255, 0.2);
}

// 功能图标
.cyber-feature-icon {
  font-size: 36px;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--accent-color-light);
  border-radius: 50%;
  flex-shrink: 0;
}

// 弹窗/模态框
.cyber-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-dark);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.cyber-modal-content {
  background: var(--bg-glass-popup);
  border: 2px solid var(--border-accent);
  border-radius: 20px;
  padding: 30px;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-strong), var(--shadow-accent);
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  transition: all 0.3s ease;
}

// ==================== 按钮系统 ====================

// 基础按钮样式
.cyber-btn {
  padding: 18px 28px;
  border-radius: 20px;
  font-size: 24px; // 增加6px (18px -> 24px)
  font-weight: 600;
  cursor: pointer;
  border: 2px solid;
  background: transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-soft);
  }

  &:active {
    transform: translateY(0);
  }
}

// 主要按钮
.cyber-btn-primary {
  @extend .cyber-btn;
  color: var(--on-primary-text);
  border-color: var(--primary-color);
  background: var(--primary-color);

  &:hover {
    background: var(--primary-color-medium);
    box-shadow: 0 8px 24px var(--primary-color-strong);
  }
}

// 次要按钮
.cyber-btn-secondary {
  @extend .cyber-btn;
  color: var(--text-secondary);
  border-color: var(--border-glass);
  width: 250px;
  min-width: 200px; // 设置最小宽度让按钮更宽
}

// ==================== 工具类 ====================

// 主题背景 - 用于页面容器
.theme-background {
  /* 叠加主色氛围渐变，让“主色=背景氛围”直观可见 */
  background:
    radial-gradient(140% 90% at 50% 10%, var(--primary-color-strong) 0%, rgba(0, 0, 0, 0) 65%),
    radial-gradient(80% 60% at 80% 80%, var(--primary-color-medium) 0%, rgba(0, 0, 0, 0) 70%),
    var(--theme-background-image) center center / cover no-repeat;
}

// 文字阴影
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

// 居中
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 垂直居中
.flex-center-vertical {
  display: flex;
  align-items: center;
}

// 间距
.gap-small {
  gap: 8px;
}
.gap-medium {
  gap: 16px;
}
.gap-large {
  gap: 24px;
}

// 边距
.mb-small {
  margin-bottom: 8px;
}
.mb-medium {
  margin-bottom: 16px;
}
.mb-large {
  margin-bottom: 24px;
}
