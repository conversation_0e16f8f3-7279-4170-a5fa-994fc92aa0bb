// 主题预设与组合生成器（按截图：氛围样式 × 主题色系）
// 注意：尽量保持轻量算法，输出 IThemeConfig 兼容结构，供 themes.ts 引用。

// Tone（氛围样式）
export type ToneMode = 'soft' | 'vivid' | 'bright' | 'dark';

// Palette（主题色系）
export interface IPalettePreset {
  id: string; // 例如 warm-orange
  name: string; // 例如 温暖橙调
  baseColor: string; // 十六进制
  accentColor?: string; // 可选的强调色基准
}

// IThemeColors & IThemeConfig 简化声明，避免循环依赖
interface IThemeColors {
  primaryColor: string;
  primaryColorLight: string;
  primaryColorMedium: string;
  primaryColorStrong: string;
  // 在主色背景上的文字颜色（自动对比度）
  onPrimaryText: string;
  accentColor: string;
  accentColorLight: string;
  accentColorMedium: string;
  accentColorStrong: string;
  bgGlass: string;
  bgGlassHover: string;
  bgGlassPopup: string;
  borderGlass: string;
  borderAccent: string;
  shadowSoft: string;
  shadowStrong: string;
  shadowAccent: string;
  overlayDark: string;
  bgInput: string;
  bgInputFocus: string;
  placeholderColor: string;
  successColor: string;
  successColorLight: string;
  successColorMedium: string;
  successColorStrong: string;
  errorColor: string;
  errorColorLight: string;
  errorColorMedium: string;
  errorColorStrong: string;
  iconFilterPrimary: string;
  iconFilterAccent: string;
  iconFilterWhite: string;
  pageTextPrimary: string;
  pageTextSecondary: string;
  pageTextTertiary: string;
  personDetailTitle: string;
  personDetailTimestamp: string;
  personDetailContext: string;
  chatItemUserContext: string;
  chatItemAssistantContext: string;
  chatItemPreResponse: string;
}

export interface IThemeConfig {
  id: string;
  name: string;
  backgroundImage: string;
  colors: IThemeColors;
}

// 12 个色系（按截图文案）
export const PALETTE_PRESETS: IPalettePreset[] = [
  { id: 'warm-orange', name: '温暖橙调', baseColor: '#FF7A00' }, // 纯橙色，和金黄/琥珀拉开
  { id: 'fresh-blue', name: '清新蓝调', baseColor: '#2AA1FF' }, // 天空蓝，和午夜蓝/青色区分
  { id: 'natural-green', name: '自然绿调', baseColor: '#2ECC71' }, // 明亮草绿，和森林深绿区分
  { id: 'elegant-purple', name: '优雅紫调', baseColor: '#7E5BEF' }, // 纯紫，和薰衣草偏淡紫区分
  { id: 'dawn-pink', name: '晚霞粉调', baseColor: '#FF2D95' }, // 鲜艳洋红粉，深饱和
  { id: 'deep-cyan', name: '深海青调', baseColor: '#00B5A9' }, // 偏青绿的青
  { id: 'sakura-pink', name: '樱花粉调', baseColor: '#FFD3E2' }, // 很浅的淡粉，明显更亮
  { id: 'forest-green', name: '森林深绿', baseColor: '#1E7A46' }, // 深绿，和自然绿拉开
  { id: 'lavender', name: '薰衣草紫', baseColor: '#B388FF' }, // 淡紫，和优雅紫区分
  { id: 'autumn-gold', name: '秋叶金黄', baseColor: '#FFD000' }, // 明亮黄金，向纯黄靠拢
  { id: 'midnight-blue', name: '午夜蓝调', baseColor: '#274690' }, // 深靛蓝，和清新蓝区分
  { id: 'amber', name: '琥珀橘调', baseColor: '#FF3D00' }, // 橘红（近朱砂），与纯橙/金黄差异大
];

// 背景图：不同氛围可走不同强度（此处复用单一图以保持简洁）
const DEFAULT_BG = '';

// 工具：生成不同透明度的 rgba
function rgba(hex: string, alpha: number) {
  const cleaned = hex.replace('#', '');
  const full =
    cleaned.length === 3
      ? cleaned
          .split('')
          .map((c) => c + c)
          .join('')
      : cleaned;
  const r = parseInt(full.slice(0, 2), 16);
  const g = parseInt(full.slice(2, 4), 16);
  const b = parseInt(full.slice(4, 6), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

// 计算主色在何种文字颜色下对比度更好（返回黑或白）
function onColorFor(hex: string) {
  const cleaned = hex.replace('#', '');
  const full =
    cleaned.length === 3
      ? cleaned
          .split('')
          .map((c) => c + c)
          .join('')
      : cleaned;
  const r = parseInt(full.slice(0, 2), 16);
  const g = parseInt(full.slice(2, 4), 16);
  const b = parseInt(full.slice(4, 6), 16);
  // 相对亮度估算 (sRGB 简化)
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  return luminance > 0.6 ? '#0b0f14' : '#ffffff';
}

// 工具：生成强调色（简单偏移，若未提供 accentColor，则基于 baseColor 提亮/偏蓝）
function deriveAccent(base: string) {
  // 简化：用更高亮度作为 accent
  return base;
}

function buildSurfaceByTone(tone: ToneMode, base: string) {
  switch (tone) {
    case 'soft':
      return {
        // 柔和模式：用主色调作为背景基调，文字用深色
        bgGlass: rgba(base, 0.08),
        bgGlassHover: rgba(base, 0.12),
        bgGlassPopup: rgba(base, 0.15),
        borderGlass: rgba(base, 0.2),
        overlayDark: rgba(base, 0.1),
        bgInput: rgba(base, 0.05),
        bgInputFocus: rgba(base, 0.1),
        text: {
          pageTextPrimary: '#374151',
          pageTextSecondary: '#6b7280',
          pageTextTertiary: '#9ca3af',
        },
      };
    case 'vivid':
      return {
        // 鲜艳模式：用主色调作为强烈背景基调，文字用白色
        bgGlass: rgba(base, 0.25),
        bgGlassHover: rgba(base, 0.35),
        bgGlassPopup: rgba(base, 0.4),
        borderGlass: rgba('#FFFFFF', 0.3),
        overlayDark: rgba(base, 0.2),
        bgInput: rgba('#FFFFFF', 0.1),
        bgInputFocus: rgba('#FFFFFF', 0.15),
        text: {
          pageTextPrimary: '#ffffff',
          pageTextSecondary: 'rgba(255,255,255,0.92)',
          pageTextTertiary: 'rgba(255,255,255,0.8)',
        },
      };
    case 'bright':
      return {
        // 明亮模式：用主色调作为淡背景基调，文字用深色
        bgGlass: rgba(base, 0.05),
        bgGlassHover: rgba(base, 0.08),
        bgGlassPopup: rgba(base, 0.1),
        borderGlass: rgba(base, 0.15),
        overlayDark: rgba(base, 0.05),
        bgInput: rgba(base, 0.03),
        bgInputFocus: rgba(base, 0.06),
        text: {
          pageTextPrimary: '#1f2937',
          pageTextSecondary: '#4b5563',
          pageTextTertiary: '#9ca3af',
        },
      };
    case 'dark':
    default:
      return {
        // 暗色模式：用主色调作为深色背景基调，文字用白色
        bgGlass: rgba(base, 0.15),
        bgGlassHover: rgba(base, 0.25),
        bgGlassPopup: rgba(base, 0.3),
        borderGlass: rgba('#FFFFFF', 0.2),
        overlayDark: rgba(base, 0.2),
        bgInput: rgba('#FFFFFF', 0.08),
        bgInputFocus: rgba('#FFFFFF', 0.14),
        text: {
          pageTextPrimary: '#ffffff',
          pageTextSecondary: 'rgba(255,255,255,0.92)',
          pageTextTertiary: 'rgba(255,255,255,0.8)',
        },
      };
  }
}

function buildIconFilters(base: string) {
  return {
    iconFilterPrimary: 'brightness(0) invert(1)',
    iconFilterAccent: 'brightness(0) invert(1)',
    iconFilterWhite: 'brightness(0) invert(1)',
  };
}

function buildShadows(base: string) {
  return {
    shadowSoft: '0 8px 24px rgba(0,0,0,0.12)',
    shadowStrong: '0 20px 60px rgba(0,0,0,0.28)',
    shadowAccent: `0 0 20px ${rgba(base, 0.25)}`,
  };
}

function buildSemantic() {
  return {
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',
  };
}

function themeName(tone: ToneMode, paletteName: string) {
  const toneCN = { soft: '柔和', vivid: '鲜艳', bright: '明亮', dark: '暗色' }[tone];
  return `${toneCN} · ${paletteName}`;
}

export function buildCompositeThemes(): IThemeConfig[] {
  const results: IThemeConfig[] = [];
  const semantic = buildSemantic();

  const tones: ToneMode[] = ['soft', 'vivid', 'bright', 'dark'];

  tones.forEach((tone) => {
    PALETTE_PRESETS.forEach((p) => {
      const primary = p.baseColor;
      const accent = deriveAccent(p.accentColor || p.baseColor);
      const surface = buildSurfaceByTone(tone, primary);
      const icons = buildIconFilters(primary);
      const shadows = buildShadows(primary);

      const colors: IThemeColors = {
        // 主色调：用于按钮、强调元素等，基于用户选择的颜色
        primaryColor: primary,
        primaryColorLight: rgba(primary, 0.12),
        primaryColorMedium: rgba(primary, 0.22),
        primaryColorStrong: rgba(primary, 0.32),
        onPrimaryText: onColorFor(primary),
        // 强调色：用于特殊高亮，稍微调亮主色
        accentColor: accent,
        accentColorLight: rgba(accent, 0.12),
        accentColorMedium: rgba(accent, 0.22),
        accentColorStrong: rgba(accent, 0.32),
        bgGlass: surface.bgGlass,
        bgGlassHover: surface.bgGlassHover,
        bgGlassPopup: surface.bgGlassPopup,
        borderGlass: surface.borderGlass,
        borderAccent: rgba(accent, 0.4),
        shadowSoft: shadows.shadowSoft,
        shadowStrong: shadows.shadowStrong,
        shadowAccent: shadows.shadowAccent,
        overlayDark: surface.overlayDark,
        bgInput: surface.bgInput,
        bgInputFocus: surface.bgInputFocus,
        placeholderColor: surface.text.pageTextSecondary,
        successColor: semantic.successColor,
        successColorLight: semantic.successColorLight,
        successColorMedium: semantic.successColorMedium,
        successColorStrong: semantic.successColorStrong,
        errorColor: semantic.errorColor,
        errorColorLight: semantic.errorColorLight,
        errorColorMedium: semantic.errorColorMedium,
        errorColorStrong: semantic.errorColorStrong,
        iconFilterPrimary: icons.iconFilterPrimary,
        iconFilterAccent: icons.iconFilterAccent,
        iconFilterWhite: icons.iconFilterWhite,
        pageTextPrimary: surface.text.pageTextPrimary,
        pageTextSecondary: surface.text.pageTextSecondary,
        pageTextTertiary: surface.text.pageTextTertiary,
        personDetailTitle: accent,
        personDetailTimestamp: surface.text.pageTextSecondary,
        personDetailContext: surface.text.pageTextPrimary,
        chatItemUserContext: surface.text.pageTextPrimary,
        chatItemAssistantContext: surface.text.pageTextSecondary,
        chatItemPreResponse: rgba(accent, 0.8),
      };

      results.push({
        id: `${tone}-${p.id}`,
        name: themeName(tone, p.name),
        backgroundImage: DEFAULT_BG,
        colors,
      });
    });
  });

  return results;
}

export const TONE_OPTIONS: { id: ToneMode; name: string }[] = [
  { id: 'soft', name: '柔和' },
  { id: 'vivid', name: '鲜艳' },
  { id: 'bright', name: '明亮' },
  { id: 'dark', name: '暗色' },
];
