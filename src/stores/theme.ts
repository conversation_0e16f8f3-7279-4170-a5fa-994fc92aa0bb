import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import { saveColorTheme } from '@/apis/user_config';
import type { IThemeConfig } from '@/config/themes';
import { availableThemes, getThemeById, getDefaultTheme } from '@/config/themes';
import { getThemeConfig } from '@/apis/common';

// 本地存储键名
const THEME_STORAGE_KEY = 'app-theme-id';

export const useThemeStore = defineStore('theme', () => {
  // 当前主题ID
  const currentThemeId = ref<string>('cyberpunk');
  // 可选：当前登录用户ID（由业务层注入），用于与服务器同步主题
  const currentUserId = ref<string | null>(null);

  // 当前主题配置
  const currentTheme = computed<IThemeConfig>(() => {
    return getThemeById(currentThemeId.value) || getDefaultTheme();
  });

  // 所有可用主题
  const themes = computed(() => availableThemes);

  // 初始化主题
  const initTheme = async () => {
    // 从本地存储读取主题ID
    const savedThemeId = localStorage.getItem(THEME_STORAGE_KEY);
    if (savedThemeId && getThemeById(savedThemeId)) {
      currentThemeId.value = savedThemeId;
    }

    // 先应用本地主题
    applyTheme(currentTheme.value);

    // 如果有用户ID，尝试从后端同步主题配置
    if (currentUserId.value) {
      try {
        console.log('🔄 [ThemeStore] 从后端获取主题配置...');
        const response = await getThemeConfig(currentUserId.value);

        if (response.result === 'success' && response.config.color_theme) {
          const backendThemeId = response.config.color_theme;

          // 检查后端主题是否与本地不同
          if (backendThemeId !== currentThemeId.value) {
            console.log(
              `🔄 [ThemeStore] 后端主题 (${backendThemeId}) 与本地主题 (${currentThemeId.value}) 不同，应用后端主题`,
            );

            // 验证后端主题是否有效
            const backendTheme = getThemeById(backendThemeId);
            if (backendTheme) {
              currentThemeId.value = backendThemeId;
              applyTheme(backendTheme);

              // 更新本地存储
              localStorage.setItem(THEME_STORAGE_KEY, backendThemeId);
              console.log(`✅ [ThemeStore] 已同步后端主题: ${backendTheme.name}`);
            } else {
              console.warn(`⚠️ [ThemeStore] 后端主题ID无效: ${backendThemeId}`);
            }
          } else {
            console.log('✅ [ThemeStore] 本地主题与后端一致，无需同步');
          }
        }
      } catch (error) {
        console.error('❌ [ThemeStore] 获取后端主题配置失败:', error);
        // 失败时继续使用本地主题，不影响用户体验
      }
    }
  };

  // 切换主题
  const switchTheme = async (themeId: string) => {
    const theme = getThemeById(themeId);
    if (theme) {
      currentThemeId.value = themeId;
      applyTheme(theme);

      // 保存到本地存储
      localStorage.setItem(THEME_STORAGE_KEY, themeId);

      // 如果有用户ID，同时保存到后端
      if (currentUserId.value) {
        try {
          console.log(`🔄 [ThemeStore] 保存主题配置到后端: ${themeId}`);
          const response = await saveColorTheme({
            user_id: currentUserId.value,
            color_theme: themeId,
          });

          if (response.result === 'success') {
            console.log(`✅ [ThemeStore] 主题配置已保存到后端: ${theme.name}`);
          } else {
            console.warn('⚠️ [ThemeStore] 后端保存主题配置失败:', response.message);
          }
        } catch (error) {
          console.error('❌ [ThemeStore] 保存主题配置到后端失败:', error);
          // 失败时不影响本地主题切换
        }
      }
    }
  };

  // 应用主题到CSS变量
  const applyTheme = (theme: IThemeConfig) => {
    const root = document.documentElement;

    // 更新CSS变量
    root.style.setProperty('--primary-color', theme.colors.primaryColor);
    root.style.setProperty('--primary-color-light', theme.colors.primaryColorLight);
    root.style.setProperty('--primary-color-medium', theme.colors.primaryColorMedium);
    root.style.setProperty('--primary-color-strong', theme.colors.primaryColorStrong);
    // 主色背景上的文字对比色
    // 注意：依赖于 theme-presets.ts 生成的 onPrimaryText
    // 若某些旧主题未提供该字段，可回退为白色
    // 可选字段 onPrimaryText：如未提供则回退为白色
    root.style.setProperty('--on-primary-text', theme.colors.onPrimaryText || '#ffffff');

    root.style.setProperty('--accent-color', theme.colors.accentColor);
    root.style.setProperty('--accent-color-light', theme.colors.accentColorLight);
    root.style.setProperty('--accent-color-medium', theme.colors.accentColorMedium);
    root.style.setProperty('--accent-color-strong', theme.colors.accentColorStrong);

    root.style.setProperty('--bg-glass', theme.colors.bgGlass);
    root.style.setProperty('--bg-glass-hover', theme.colors.bgGlassHover);
    root.style.setProperty('--bg-glass-popup', theme.colors.bgGlassPopup);
    root.style.setProperty('--border-glass', theme.colors.borderGlass);
    root.style.setProperty('--border-accent', theme.colors.borderAccent);

    root.style.setProperty('--shadow-soft', theme.colors.shadowSoft);
    root.style.setProperty('--shadow-strong', theme.colors.shadowStrong);
    root.style.setProperty('--shadow-accent', theme.colors.shadowAccent);

    root.style.setProperty('--overlay-dark', theme.colors.overlayDark);
    root.style.setProperty('--bg-input', theme.colors.bgInput);
    root.style.setProperty('--bg-input-focus', theme.colors.bgInputFocus);

    root.style.setProperty('--success-color', theme.colors.successColor);
    root.style.setProperty('--success-color-light', theme.colors.successColorLight);
    root.style.setProperty('--success-color-medium', theme.colors.successColorMedium);
    root.style.setProperty('--success-color-strong', theme.colors.successColorStrong);

    root.style.setProperty('--error-color', theme.colors.errorColor);
    root.style.setProperty('--error-color-light', theme.colors.errorColorLight);
    root.style.setProperty('--error-color-medium', theme.colors.errorColorMedium);
    root.style.setProperty('--error-color-strong', theme.colors.errorColorStrong);

    // 更新背景图片变量
    root.style.setProperty('--theme-background-image', `url('${theme.backgroundImage}')`);

    // 更新页面文字颜色变量
    root.style.setProperty('--page-text-primary', theme.colors.pageTextPrimary);
    root.style.setProperty('--page-text-secondary', theme.colors.pageTextSecondary);
    root.style.setProperty('--page-text-tertiary', theme.colors.pageTextTertiary);

    // 同步通用文本变量到当前主题（许多组件使用 --text-*，需要随主题适配）
    root.style.setProperty('--text-primary', theme.colors.pageTextPrimary);
    root.style.setProperty('--text-secondary', theme.colors.pageTextSecondary);
    root.style.setProperty('--text-tertiary', theme.colors.pageTextTertiary);

    // 更新输入框placeholder颜色变量
    root.style.setProperty('--placeholder-color', theme.colors.placeholderColor);

    // 更新PersonDetail组件专用文字颜色变量
    root.style.setProperty('--person-detail-title', theme.colors.personDetailTitle);
    root.style.setProperty('--person-detail-timestamp', theme.colors.personDetailTimestamp);
    root.style.setProperty('--person-detail-context', theme.colors.personDetailContext);

    // 更新ChatItem组件专用文字颜色变量
    root.style.setProperty('--chat-item-user-context', theme.colors.chatItemUserContext);
    root.style.setProperty('--chat-item-assistant-context', theme.colors.chatItemAssistantContext);
    root.style.setProperty('--chat-item-pre-response', theme.colors.chatItemPreResponse);

    console.log(`🎨 [ThemeStore] 已应用主题: ${theme.name}`, theme.backgroundImage);
  };

  // 获取下一个主题（用于循环切换）
  const getNextTheme = () => {
    const currentIndex = themes.value.findIndex((theme) => theme.id === currentThemeId.value);
    const nextIndex = (currentIndex + 1) % themes.value.length;
    return themes.value[nextIndex];
  };

  // 切换到下一个主题
  const switchToNextTheme = async () => {
    const nextTheme = getNextTheme();
    await switchTheme(nextTheme.id);
  };

  // 监听主题变化
  watch(currentTheme, (newTheme) => {
    console.log(`🎨 [ThemeStore] 主题已切换到: ${newTheme.name}`);
  });

  return {
    // 状态
    currentThemeId,
    currentTheme,
    themes,
    currentUserId,

    // 方法
    initTheme,
    switchTheme,
    switchToNextTheme,
    applyTheme,
    getNextTheme,
    // 业务层可在登录后注入 userId
    setUserId: (userId: string) => {
      currentUserId.value = userId;
    },
  };
});
